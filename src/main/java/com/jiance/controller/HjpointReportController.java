package com.jiance.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.extra.compress.CompressUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.jiance.common.base.BaseController;
import com.jiance.common.base.Basepoint;
import com.jiance.common.base.Result;
import com.jiance.common.handler.MyException;
import com.jiance.common.utils.BisConstant;
import com.jiance.common.utils.FileUtils;
import com.jiance.model.entity.*;
import com.jiance.service.ICityjdqsService;
import com.jiance.service.IHjpointReportService;
import com.jiance.service.impl.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.ArchiveStreamFactory;
import org.apache.poi.xwpf.usermodel.*;
import org.jetbrains.annotations.NotNull;
import org.postgis.Point;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 混接点与支管暗接调查表	 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-04
 */
@Tag(name = "混接点与支管暗接调查表")
@RestController
@Slf4j
@RequestMapping("/hjpointReport")
public class HjpointReportController extends BaseController<HjpointReport, IHjpointReportService> {

    @Value(value = "${jc.file.root-path:/run/media/root/}")
    private String fileRootPath;

    @Resource
    YspointServiceImpl yspointService;

    @Resource
    WspointServiceImpl wspointService;

    final JcFileBusinessServiceImpl jcFileBusinessService;

    @Resource
    PersonnelServiceImpl personnelService;

    @Resource
    CityareaServiceImpl cityareaService;

    // @Resource
    // QuyuServiceImpl quyuService;

    @Resource
    ICityjdqsService cityjdqsService;

    @Override
    public void setDefaultValue(HjpointReport hjpointReport) {

    }

    @Override
    public void fillEntity(HjpointReport hjpointReport) {
        List<JcFileBusiness> jcFileBusinessList = jcFileBusinessService.list(
                Wrappers.<JcFileBusiness>lambdaQuery().eq(JcFileBusiness::getBusinessId, hjpointReport.getId())
                        .orderByAsc(JcFileBusiness::getCreateTime));
        hjpointReport.setJcFileBusinessList(jcFileBusinessList);
    }

    @Override
    public List<HjpointReport> pageRecords(List<HjpointReport> list) throws Exception {
        List<HjpointReport> reportList = super.pageRecords(list);
        reportList.forEach(i -> {
            try {
                fillEntity(i);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return reportList;
    }

    @Override
    public HjpointReport getEntity(String uuid) throws Exception {
        HjpointReport hjpointReport = super.getEntity(uuid);
        List<JcFileBusiness> jcFileBusinessList = jcFileBusinessService.list(
                Wrappers.<JcFileBusiness>lambdaQuery().eq(JcFileBusiness::getBusinessId, uuid)
                        .orderByAsc(JcFileBusiness::getCreateTime));
        hjpointReport.setJcFileBusinessList(jcFileBusinessList);
        return hjpointReport;
    }

    public HjpointReportController(HjpointReportServiceImpl hjpointReportService, JcFileBusinessServiceImpl jcFileBusinessService) {
        this.service = hjpointReportService;
        this.jcFileBusinessService = jcFileBusinessService;
    }

    @Override
    protected Wrapper<HjpointReport> wrapperBuilder(Map<String, Object> query) {
        QueryWrapper<HjpointReport> queryWrapper = new QueryWrapper<>();
        query.forEach((k, v) -> {
            if (ObjUtil.isNotEmpty(v)) {
                switch (k) {
                    case "id":
                        queryWrapper.eq(camel2under(k), v);
                        break;
                    case "ssqy":
                        queryWrapper.eq(camel2under(k), Integer.parseInt((String) v));
                        break;
                    case "dh":
                    case "dj":
                    case "hjfs":
                    case "ssdl":
                    case "jrstxz":
                        queryWrapper.like(camel2under(k), v);
                        break;
                    case "ids":
                        List<String> ids = (List<String>) v;
                        queryWrapper.in("h.id", ids);
                        break;
                    case "perids":
                        List<String> perids = (List<String>) v;
                        if (ObjUtil.isNotEmpty(perids)) {
                            queryWrapper.in("h.perid", perids);
                        }
                        break;
                    default:
                        break;
                }
            }
        });
        queryWrapper.orderByDesc("tzh");
        return queryWrapper;
    }

    /**
     * 无排序查询条件构造
     *
     * @param query 查询条件
     * @return 查询条件构造器
     */
    protected Wrapper<HjpointReport> wrapperBuilderNoOrder(Map<String, Object> query) {
        QueryWrapper<HjpointReport> queryWrapper = new QueryWrapper<>();
        query.forEach((k, v) -> {
            if (ObjUtil.isNotEmpty(v)) {
                switch (k) {
                    case "id":
                        queryWrapper.eq(camel2under(k), v);
                        break;
                    case "dh":
                    case "dj":
                    case "hjfs":
                    case "jrstxz":
                        queryWrapper.like(camel2under(k), v);
                        break;
                    case "ssqy":
                        queryWrapper.eq(camel2under(k), Integer.parseInt(v.toString()));
                        break;
                    case "ids":
                        List<String> ids = (List<String>) v;
                        queryWrapper.in("id", ids);
                        break;
                    case "perids":
                        List<String> perids = (List<String>) v;
                        if (ObjUtil.isNotEmpty(perids)) {
                            queryWrapper.in("perid", perids);
                        }
                        break;
                    default:
                        break;
                }
            }
        });
        return queryWrapper;
    }

    @Override
    public Result<HjpointReport> page(@RequestParam Map<String, Object> query) throws Exception {
        IPage<HjpointReport> page = iPageBuilder(query);
        if (ObjUtil.isEmpty(query.get("perid"))) {
            throw new MyException(BisConstant.SYSTEM_REQUEST_ZERO);
        }
        query.put("perids", getUserIdsByPerid(Integer.parseInt(query.get("perid").toString())));
        Wrapper<HjpointReport> wrapper = wrapperBuilder(query);
        handleSort(query, page);
        IPage<HjpointReport> s = service.page2((Page<HjpointReport>) page, wrapper);
        return page2result(s);
    }

    /**
     * 判读用户角色，项目经理可以看到remark相同的所有人上传的数据
     * 普通作业员可以看自己上传的数据
     *
     * @param perid 用户id
     * @return 用户id集合
     */
    private List<Integer> getUserIdsByPerid(Integer perid) throws MyException {
        List<Integer> perids = new ArrayList<>();
        Personnel personnel = personnelService.getById(perid);
        if (personnel == null) {
            throw new MyException(BisConstant.SYSTEM_NON_EXIST);
        }
        if (personnel.getPersona() != null && personnel.getPersona().equals("作业员")) {
            perids.add(perid);
        } else if (personnel.getRemarks() != null && personnel.getRemarks().equals("烟台市")) {
            // 如果备注为烟台市，则查询烟台市下所有数据
            List<String> remarks = cityareaService.listObjs(Wrappers.<Cityarea>lambdaQuery().select(Cityarea::getArea).eq(Cityarea::getCity, personnel.getRemarks()), Object::toString);
            if (ObjUtil.isNotEmpty(remarks)) {
                perids = personnelService.listObjs(Wrappers.<Personnel>lambdaQuery().select(Personnel::getId).in(Personnel::getRemarks, remarks), s -> Integer.valueOf(s.toString()));
            } else {
                perids = personnelService.listObjs(Wrappers.<Personnel>lambdaQuery().select(Personnel::getId).eq(Personnel::getRemarks, personnel.getRemarks()), s -> Integer.valueOf(s.toString()));
            }
        } else {
            perids = personnelService.listObjs(Wrappers.<Personnel>lambdaQuery().select(Personnel::getId).eq(Personnel::getRemarks, personnel.getRemarks()), s -> Integer.valueOf(s.toString()));
        }
        return perids;
    }

    @PostMapping("/saveOrUpdate2")
    public Result<String> saveOrUpdate2(HjpointReport hjpointReport, @RequestParam(required = false) MultipartFile[] files) throws Exception {
        if (ObjUtil.isEmpty(hjpointReport.getId()) && service.count(Wrappers.<HjpointReport>lambdaQuery().eq(HjpointReport::getDh, hjpointReport.getDh())) > 0) {
            throw new MyException("混接调查点点号重复");
        }
        setSexpAndEexp(hjpointReport);
        Result<String> result = super.saveOrUpdate(hjpointReport);
        String businessId = (String) result.getDataSingle();
        if (ObjUtil.isNotEmpty(files)) {
            // jcFileBusinessService.remove(Wrappers.<JcFileBusiness>lambdaQuery().eq(JcFileBusiness::getBusinessId, businessId));
            String imageDirRoot = "hjPoint/" + DateUtil.format(new Date(), "yyyyMMddHHmmss");
            for (MultipartFile file : files) {
                saveImageFile(hjpointReport, imageDirRoot, file);
            }
        }
        return result;
    }

    /**
     * 设置混接起点、终点
     *
     * @param hjpointReport 混接调查表实体
     */
    private void setSexpAndEexp(HjpointReport hjpointReport) {
        // 分隔点号字段，获取混接起点、终点，可能存在问题，“-”在点号中可能出现多次
        String[] dhArr = hjpointReport.getDh().split("-(?=[A-Za-z])");
        if (dhArr.length == 1) {
            setEexp(hjpointReport, dhArr[0], hjpointReport.getProject());
        } else {
            // 设置sexp、eexp点号与坐标
            setSexp(hjpointReport, dhArr[0], hjpointReport.getProject());
            setEexp(hjpointReport, dhArr[1], hjpointReport.getProject());
        }
    }

    /**
     * 设置混接起点
     *
     * @param hjpointReport 实体
     * @param sExpNo        起点点号
     */
    private void setSexp(HjpointReport hjpointReport, String sExpNo, String project) {
        Basepoint sPoint = yspointService.getOne(Wrappers.<Yspoint>lambdaQuery().eq(Yspoint::getExpNo, sExpNo)
                .eq(Yspoint::getProject, project)
                .last("limit 1"));
        if (sPoint == null) {
            sPoint = wspointService.getOne(Wrappers.<Wspoint>lambdaQuery().eq(Wspoint::getExpNo, sExpNo)
                    .eq(Wspoint::getProject, project)
                    .last("limit 1"));
        }
        hjpointReport.setSexp(sExpNo);
        if (sPoint != null) {
            hjpointReport.setSx(sPoint.getX());
            hjpointReport.setSy(sPoint.getY());
        }
    }

    /**
     * 设置混接终点
     *
     * @param hjpointReport 实体
     * @param eExpNo        终点点号
     */
    private void setEexp(HjpointReport hjpointReport, String eExpNo, String project) {
        Basepoint sPoint = yspointService.getOne(Wrappers.<Yspoint>lambdaQuery().eq(Yspoint::getExpNo, eExpNo)
                .eq(Yspoint::getProject, project)
                .last("limit 1"));
        if (sPoint == null) {
            sPoint = wspointService.getOne(Wrappers.<Wspoint>lambdaQuery().eq(Wspoint::getExpNo, eExpNo)
                    .eq(Wspoint::getProject, project)
                    .last("limit 1"));
        }
        // 如果 结束坐标没找到就赋值为起始坐标
        if (sPoint == null) {
            sPoint = yspointService.getOne(Wrappers.<Yspoint>lambdaQuery().eq(Yspoint::getExpNo, eExpNo)
                    .eq(Yspoint::getProject, project)
                    .last("limit 1"));
            if (sPoint == null) {
                sPoint = wspointService.getOne(Wrappers.<Wspoint>lambdaQuery().eq(Wspoint::getExpNo, eExpNo)
                        .eq(Wspoint::getProject, project)
                        .last("limit 1"));
            }
        }

        hjpointReport.setEexp(eExpNo);
        if (sPoint != null) {
            hjpointReport.setX(sPoint.getX());
            hjpointReport.setY(sPoint.getY());
            hjpointReport.setGeom(new Point(sPoint.getY().doubleValue(), sPoint.getX().doubleValue()));
        }
    }

    @Operation(summary = "保存并导出混接调查表")
    @PostMapping("/saveAndExprotDocx")
    public void saveAndExprotDocx(HjpointReport hjpointReport, MultipartFile[] files, HttpServletResponse response) throws IOException, MyException {
        service.saveOrUpdate(hjpointReport);
        if (ObjUtil.isNotEmpty(files)) {
            String imageDirRoot = "hjPoint/" + DateUtil.format(new Date(), "yyyyMMddHHmmss");
            for (int i = 0; i < files.length; i++) {
                String imageFilePath = saveImageFile(hjpointReport, imageDirRoot, files[i]);
                fillImage4Render(hjpointReport, i, imageFilePath);
            }
        }
        InputStream tempStream = ResourceUtil.getStream("temp/temp_hjdcb.docx");
        XWPFTemplate template = XWPFTemplate.compile(tempStream).render(hjpointReport);
        String fileName = URLEncoder.encode("混接调查表-" + hjpointReport.getDh() + DateUtil.format(new Date(), "yyyyMMddHHmmss"), "UTF-8");
        response.setContentType("application/octet-stream;charset=UTF-8");
        // 设置响应头信息header，下载时以文件附件下载
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".docx");
        template.writeAndClose(response.getOutputStream());
    }

    /**
     * 填充图片
     *
     * @param hjpointReport 实体
     * @param i             图片索引
     * @param imageFilePath 图片路径
     */
    private void fillImage4Render(HjpointReport hjpointReport, int i, String imageFilePath) throws IOException {
        PictureRenderData pictureRenderData = null;
        if (imageFilePath.toLowerCase().endsWith(".jfif")) {
            pictureRenderData = Pictures.ofStream(Files.newInputStream(Paths.get(imageFilePath))).size(280, 210).create();
        } else {
            pictureRenderData = Pictures.ofLocal(imageFilePath).size(280, 210).create();
        }

        if (i == 0) {
            hjpointReport.setImage1(pictureRenderData);
        } else if (i == 1) {
            hjpointReport.setImage2(pictureRenderData);
        } else if (i == 2) {
            hjpointReport.setImage3(pictureRenderData);
        } else if (i == 3) {
            hjpointReport.setImage4(pictureRenderData);
        }
    }

    /**
     * 保存图片文件
     *
     * @param hjpointReport 实体
     * @param imageDirRoot  图片文件夹相对目录
     * @param file          文件
     * @return 图片文件相对路径
     * @throws IOException IO异常
     */
    @NotNull
    private String saveImageFile(HjpointReport hjpointReport, String imageDirRoot, MultipartFile file) throws IOException, MyException {
        String absoluteDirPath = fileRootPath + "ZFQ" + File.separator + imageDirRoot;
        File file1 = new File(absoluteDirPath);
        if (!file1.exists() && !file1.mkdirs()) {
            throw new MyException("创建文件夹失败");
        }
        String filename = file.getOriginalFilename();
        String imageFilePath = absoluteDirPath + "/" + filename;
        FileUtil.writeFromStream(file.getInputStream(), imageFilePath);
        JcFileBusiness fileBusiness = new JcFileBusiness();
        fileBusiness.setBusinessId(hjpointReport.getId());
        fileBusiness.setFileName(filename);
        fileBusiness.setCreateBy(String.valueOf(hjpointReport.getPerid()));
        fileBusiness.setPath(imageDirRoot + "/" + filename);
        fileBusiness.setCreateTime(LocalDateTimeUtil.now());
        jcFileBusinessService.saveOrUpdate(fileBusiness);
        return imageFilePath;
    }


    /**
     * 根据id导出混接调查表
     *
     * @param uuid     混接记录id
     * @param response response
     * @throws Exception 异常
     */
    @Operation(summary = "导出混接调查表")
    @GetMapping("/exportHjdcb")
    public void exportHjdcb(String uuid, HttpServletResponse response) throws Exception {
        HjpointReport hjpointReport = service.getById(uuid);
        fillImage(hjpointReport);
        InputStream tempStream = ResourceUtil.getStream("temp/temp_hjdcb.docx");
        XWPFTemplate template = XWPFTemplate.compile(tempStream).render(hjpointReport);
        String fileName = URLEncoder.encode("混接调查表-" + hjpointReport.getSsdl() + "-" + hjpointReport.getDh() + "-" + DateUtil.format(new Date(), "yyyyMMddHHmmss"), "UTF-8");
        response.setContentType("application/octet-stream;charset=UTF-8");
        // 设置响应头信息header，下载时以文件附件下载
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".docx");
        template.writeAndClose(response.getOutputStream());
    }

    /**
     * 批量导出混接调查表压缩包
     *
     * @param query    查询条件
     * @param response response
     * @throws MyException 异常
     * @throws IOException IO异常
     */
    @Operation(summary = "批量导出混接调查表")
    @PostMapping("/batchExportHjdcb")
    public void batchExportHjdcb(@RequestBody Map<String, Object> query, HttpServletResponse response) throws MyException, IOException {
        if (ObjUtil.isEmpty(query.get("perid"))) {
            throw new MyException(BisConstant.SYSTEM_REQUEST_ZERO);
        }
        query.put("perids", getUserIdsByPerid(Integer.parseInt(query.get("perid").toString())));
        List<HjpointReport> reportList = service.list2(wrapperBuilder(query));
        String tempPath = "temp/" + IdUtil.fastSimpleUUID() + "/";
        String absoluteDirPath = fileRootPath + "ZFQ" + File.separator + tempPath;
        File file1 = new File(absoluteDirPath);
        if (!file1.exists() && !file1.mkdirs()) {
            throw new MyException("创建文件夹失败");
        }
        if (ObjUtil.isNotEmpty(reportList)) {
            reportList.forEach(i -> {
                fillImage(i);
                InputStream tempStream = ResourceUtil.getStream("temp/temp_hjdcb.docx");
                XWPFTemplate template = XWPFTemplate.compile(tempStream).render(i);
                String fileName = "混接调查表-" + i.getSsdl() + "-" + i.getDh() + "-" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".docx";
                try {
                    template.write(Files.newOutputStream(Paths.get(absoluteDirPath + fileName)));
                } catch (IOException e) {
                    log.error(e.getMessage());
                    throw new RuntimeException(e);
                }
            });
        } else {
            throw new MyException(BisConstant.SYSTEM_REQUEST_ZERO);
        }
        response.setHeader("content-type", "application/octet-stream");
        response.setHeader("Access-Control-Expose-Headers", "Conthttp://ent-Disposition");
        response.setContentType("application/octet-stream;charset=UTF-8");
        // 设置响应头信息header，下载时以文件附件下载
        String outFileName = "混接调查表-" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".zip";
        response.setHeader("Content-Disposition", "attachment;filename=" + FileUtils.setFileDownloadHeader(request, outFileName));
        CompressUtil.createArchiver(CharsetUtil.CHARSET_UTF_8, ArchiveStreamFactory.ZIP, response.getOutputStream()).add(FileUtil.file(absoluteDirPath)).finish().close();
        FileUtil.del(absoluteDirPath);
    }

    /**
     * 填充图片
     *
     * @param hjpointReport 实体
     */
    public void fillImage(HjpointReport hjpointReport) {
        List<JcFileBusiness> fileBusinessList = jcFileBusinessService.list(Wrappers.<JcFileBusiness>lambdaQuery()
                .eq(JcFileBusiness::getBusinessId, hjpointReport.getId())
                .orderByAsc(JcFileBusiness::getCreateTime));
        if (ObjUtil.isEmpty(fileBusinessList)) {
            return;
        }
        for (int i = 0; i < fileBusinessList.size(); i++) {
            String imagePath = fileBusinessList.get(i).getPath();
            if (!imagePath.contains(fileRootPath)) {
                imagePath = fileRootPath + "ZFQ" + File.separator + imagePath;
            }
            try {
                fillImage4Render(hjpointReport, i, imagePath);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    /**
     * 导出混接记录台账(报表)
     *
     * @param query    查询条件
     * @param response response
     * @throws MyException 异常
     * @throws IOException IO异常
     */
    @Operation(summary = "导出混接记录台账")
    @PostMapping("/exportExcelReport")
    public void exportExcelReport(@RequestBody Map<String, Object> query, HttpServletResponse response) throws MyException, IOException {
        if (ObjUtil.isEmpty(query.get("perid"))) {
            throw new MyException(BisConstant.SYSTEM_REQUEST_ZERO);
        }
        query.put("perids", getUserIdsByPerid(Integer.parseInt(query.get("perid").toString())));
        List<HjpointReport> list = service.list2(wrapperBuilder(query));
        if (ObjUtil.isEmpty(list)) {
            throw new MyException(BisConstant.SYSTEM_REQUEST_ZERO);
        }
        // List<Quyu> quyuList = quyuService.list();
        List<Cityjdqs> cityjdqsList = cityjdqsService.list();
        for (int i = 0; i < list.size(); i++) {
            HjpointReport report = list.get(i);
            report.setIndexNum(i + 1);
            if (cityjdqsList != null) {
                report.setSsqyName(cityjdqsList.stream().filter(j -> Objects.equals(j.getId(), report.getSsqy())).findAny().orElse(new Cityjdqs()).getJd());
            }
            report.setPerName(personnelService.getById(report.getPerid()).getName());
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("混接点调查成果-" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()), "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), HjpointReport.class).sheet("成果").doWrite(list);
    }

    /**
     * 根据混接点类型（排水源类型）统计
     *
     * @return Result 统计结果
     */
    @Operation(summary = "根据混接点类型统计")
    @GetMapping("/statByHjdlx")
    public Result<Map<String, Object>> statByHjdlx(@RequestParam Map<String, Object> map) throws MyException {
        Result<Map<String, Object>> result = new Result<>();
        if (ObjUtil.isEmpty(map.get("perid"))) {
            throw new MyException(BisConstant.SYSTEM_REQUEST_ZERO);
        }
        map.put("perids", getUserIdsByPerid(Integer.parseInt(map.get("perid").toString())));
        QueryWrapper<HjpointReport> wrapper = (QueryWrapper<HjpointReport>) wrapperBuilderNoOrder(map);
        wrapper.select("hjdlx, count(*)");
        wrapper.groupBy("hjdlx");
        List<Map<String, Object>> mapList = service.listMaps(wrapper);
        mapList.forEach(i -> {
            if (ObjUtil.isEmpty(i.get("hjdlx"))) {
                i.put("hjdlx", "未知");
            }
        });
        result.setDataList(mapList);
        result.setTrue();
        return result;
    }

    /**
     * 根据接入水体性质统计
     *
     * @return Result
     */
    @Operation(summary = "根据接入水体性质统计")
    @GetMapping("/statByJrstxz")
    public Result<Map<String, Object>> statByJrstxz(@RequestParam Map<String, Object> map) throws MyException {
        Result<Map<String, Object>> result = new Result<>();
        if (ObjUtil.isEmpty(map.get("perid"))) {
            throw new MyException(BisConstant.SYSTEM_REQUEST_ZERO);
        }
        map.put("perids", getUserIdsByPerid(Integer.parseInt(map.get("perid").toString())));
        QueryWrapper<HjpointReport> wrapper = (QueryWrapper<HjpointReport>) wrapperBuilderNoOrder(map);
        wrapper.select("jrstxz, count(*)");
        wrapper.groupBy("jrstxz");
        List<Map<String, Object>> mapList = service.listMaps(wrapper);
        mapList.forEach(i -> {
            if (ObjUtil.isEmpty(i.get("jrstxz"))) {
                i.put("jrstxz", "未知");
            }
        });
        result.setDataList(mapList);
        result.setTrue();
        return result;
    }

    /**
     * 根据混接暗接等级统计
     *
     * @return Result
     */
    @Operation(summary = "根据混接暗接等级统计")
    @GetMapping("/statByDj")
    public Result<Map<String, Object>> statByDj(@RequestParam Map<String, Object> map) throws MyException {
        Result<Map<String, Object>> result = new Result<>();
        if (ObjUtil.isEmpty(map.get("perid"))) {
            throw new MyException(BisConstant.SYSTEM_REQUEST_ZERO);
        }
        map.put("perids", getUserIdsByPerid(Integer.parseInt(map.get("perid").toString())));
        QueryWrapper<HjpointReport> wrapper = (QueryWrapper<HjpointReport>) wrapperBuilderNoOrder(map);
        wrapper.select("dj, count(*)");
        wrapper.groupBy("dj");
        result.setDataList(service.listMaps(wrapper));
        result.setTrue();
        return result;
    }

    /**
     * 按区域统计
     *
     * @param map 查询条件
     * @return Result
     */
    @Operation(summary = "按区域统计")
    @GetMapping("/statByArea")
    public Result<Map<String, Object>> statByArea(@RequestParam Map<String, Object> map) throws MyException {
        Result<Map<String, Object>> result = new Result<>();
        if (ObjUtil.isEmpty(map.get("perid"))) {
            throw new MyException(BisConstant.SYSTEM_REQUEST_ZERO);
        }
        map.put("perids", getUserIdsByPerid(Integer.parseInt(map.get("perid").toString())));
        QueryWrapper<HjpointReport> wrapper = (QueryWrapper<HjpointReport>) wrapperBuilderNoOrder(map);
        if (ObjUtil.isEmpty(map.get("groupField"))) {
            throw new MyException("统计字段不能为空");
        }
        String groupField = (String) map.get("groupField");
        wrapper.select(groupField,
                "sum(case when jrstxz ='雨水' then 1 else 0 end) wscount ," +
                        "sum(case when jrstxz ='污水' then 1 else 0 end) yscount ," +
                        "sum(case when hjdlx ='商铺' then 1 else 0 end) spcount ," +
                        "sum(case when hjdlx ='居民区' then 1 else 0 end) jmqcount ," +
                        "sum(case when hjdlx ='厂区' then 1 else 0 end) cqcount ," +
                        "sum(case when hjdlx ='市政设施' then 1 else 0 end) szsscount ," +
                        "sum(case when hjdlx ='企事业单位' then 1 else 0 end) qsydwcount ");
        wrapper.groupBy(groupField);
        return result;
    }

    /**
     * 根据点号，从雨污水点表获取坐标，更新混接调查表geom字段
     *
     * @return Result
     */
    @Operation(summary = "根据点号，从雨污水点表获取坐标，更新混接调查表geom字段")
    @GetMapping("/updatePointGeom")
    public Result<String> updatePointGeom(@RequestParam Map<String, Object> map) throws MyException {
        Result<String> result = new Result<>();
        List<HjpointReport> hjpointReports = service.list(Wrappers.<HjpointReport>lambdaQuery()
                .eq(HjpointReport::getProject, map.get("tarProject"))
        );
        if (ObjUtil.isEmpty(hjpointReports)) {
            throw new MyException(BisConstant.SYSTEM_REQUEST_ZERO);
        }
        List<String> emptyPoints = new ArrayList<>();
        hjpointReports.forEach(i -> {
            setSexpAndEexp(i);
            if (i.getGeom() == null) {
                emptyPoints.add(i.getEexp());
            } else {
                service.saveOrUpdate(i);
            }

        });
        result.setTrue();
        result.setDataList(emptyPoints);
        return result;
    }


    // /**
    //  * 导入word混接调查记录表
    //  *
    //  * @param files 文件
    //  * @return Result
    //  */
    // @Operation(summary = "导入混接调查记录表")
    // @PostMapping("/importHjdcbDocx")
    // public Result<String> importHjdcbDocx(MultipartFile[] files) throws MyException, IOException {
    //     Result<String> result = new Result<>();
    //     if (ObjUtil.isEmpty(files)) {
    //         throw new MyException(BisConstant.SYSTEM_FILE_NO_EXIST);
    //     }
    //     for (MultipartFile file : files) {
    //         readHjdcb((FileInputStream) file.getInputStream());
    //     }
    //     return result;
    // }

    /**
     * 读取混接调查表
     *
     * @param inputStream 文件流
     * @throws IOException IO异常
     */
    public void readHjdcb(FileInputStream inputStream) throws IOException {
        XWPFDocument document = new XWPFDocument(inputStream);
        Iterator<XWPFTable> tableIterator = document.getTablesIterator();
        Iterator<XWPFParagraph> paragraphIterator = document.getParagraphsIterator();
        HjpointReport hjpointReport = null;
        List<String> imagePaths = new ArrayList<>();
        // 混接调查表，混接点图片存放目录按照`年/月日/小时/分钟/秒/`结构存放
        String imageDirRoot = "hjPoint/" + DateUtil.format(new Date(), "yyyy/MMdd/HH/mmss");
        String absoluteDirPath = fileRootPath + "ZFQ" + File.separator + imageDirRoot;
        while (tableIterator.hasNext()) {
            XWPFTable table = tableIterator.next();
            if (table.getRows().isEmpty()) {
                continue;
            }
            List<XWPFTableRow> rows = table.getRows();
            for (int i = 0; i < rows.size(); i++) {
                XWPFTableRow row = rows.get(i);
                for (int j = 0; j < row.getTableICells().size(); j++) {
                    XWPFTableCell cell = (XWPFTableCell) row.getTableICells().get(j);
                    if (i < 7) {
                        log.error("{}-{}：{}", i, j, cell.getText());
                        if (i == 0 && j == 1) {
                            String[] dhArr = cell.getText().split("-(?=[A-Za-z])");
                            log.error("sexp+eexp:", dhArr[0] + dhArr[1]);
                            hjpointReport = service.getOne(Wrappers.<HjpointReport>lambdaQuery()
                                    .eq(HjpointReport::getSexp, dhArr[0])
                                    .eq(dhArr.length == 2, HjpointReport::getEexp, dhArr[1])
                                    .last("limit 1"));
                        }
                    } else {
                        List<XWPFParagraph> paragraphList = cell.getParagraphs();
                        if (ObjUtil.isEmpty(paragraphList)) {
                            continue;
                        }
                        List<XWPFRun> xwpfRuns = paragraphList.get(0).getRuns();
                        if (ObjUtil.isEmpty(xwpfRuns)) {
                            continue;
                        }
                        List<XWPFPicture> pictureList = xwpfRuns.get(0).getEmbeddedPictures();
                        if (!pictureList.isEmpty()) {
                            XWPFPicture picture = pictureList.get(0);
                            InputStream picInIsData = new ByteArrayInputStream(picture.getPictureData().getData());
                            String imagePath = absoluteDirPath + File.separator + IdUtil.fastUUID() + ".png";
                            FileUtil.writeFromStream(picInIsData, imagePath);
                            imagePaths.add(imagePath);
                        }
                    }
                }
            }
        }
        if (hjpointReport != null && !imagePaths.isEmpty()) {
            jcFileBusinessService.remove(Wrappers.<JcFileBusiness>lambdaQuery().eq(JcFileBusiness::getBusinessId, hjpointReport.getId()));
            String bussinessId = hjpointReport.getId();
            Integer perid = hjpointReport.getPerid();
            imagePaths.forEach(i -> {
                JcFileBusiness fileBusiness = new JcFileBusiness();
                fileBusiness.setBusinessId(bussinessId);
                fileBusiness.setFileName(FileUtil.getName(i));
                fileBusiness.setCreateBy(String.valueOf(perid));
                fileBusiness.setPath(i);
                fileBusiness.setCreateTime(LocalDateTimeUtil.now());
                jcFileBusinessService.saveOrUpdate(fileBusiness);
            });
        }


        while (paragraphIterator.hasNext()) {
            XWPFParagraph paragraph = paragraphIterator.next();
            String text = paragraph.getText();
            if (text.startsWith("调查时间：")) {
                // TODO 处理调查时间字段
            }
        }
    }

    // /**
    //  * TODO 未完成 批量保存
    //  *
    //  * @param hjpointReportList
    //  * @return
    //  * @throws MyException
    //  */
    // @Operation(summary = "批量保存")
    // @PostMapping("/batchSave4Word")
    // public Result batchSave4Word(@RequestBody List<HjpointReport> hjpointReportList) throws MyException {
    //     Result result = new Result();
    //     if (ObjUtil.isEmpty(hjpointReportList)) {
    //         throw new MyException(BisConstant.SYSTEM_REQUEST_ZERO);
    //     }
    //     hjpointReportList.forEach(i -> {
    //
    //     });
    //     return result;
    // }
}
